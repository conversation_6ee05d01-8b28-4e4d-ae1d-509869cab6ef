import 'package:dio/dio.dart';
import 'package:neorevv/src/presentation/cubit/agent/agent_cubit.dart';
import '../../core/network/dio_client.dart';
import '../../core/services/api_error_handler.dart';
import '../../core/network/api_config.dart';
import '../../core/config/app_strings.dart';
import '../../core/services/exceptions.dart';
import '../../domain/models/agent_model.dart';
import '../../domain/repository/agent_repository.dart';

class AgentRepositoryImpl extends AgentRepository {
  AgentRepositoryImpl();

  static const String baseUrl = APIConfig.baseUrl;
  static const String agentsSearchUrl = APIConfig.agentsSearch;

  @override
  Future<ApiAgentResponse> getAgents(Map<String, dynamic> requestBody) async {
    try {
      final dio = await DioClient.getDio();

      final response = await dio.post(agentsSearchUrl, data: requestBody);

      if (response.statusCode == 200) {
        return ApiAgentResponse.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, failedToFetchAgents);
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<AgentModel> getAgentById(String agentId) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get('$agentsSearchUrl/$agentId');

      if (response.statusCode == 200) {
        return AgentModel.fromJson(response.data['data'] ?? response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, failedToFetchAgentDetails);
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<bool> createAgent(Map<String, dynamic> requestBody) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.post(APIConfig.agentCreate, data: requestBody);

      if (response.statusCode == 200 || response.statusCode == 201) {
        print('---------response.data---------');
        print(response.data);
        return true;
      } else {
        return false;
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, 'Failed to create agent');
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<bool> uploadAgentFile(Map<String, dynamic> requestBody) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.post(
        APIConfig.agentFileUpload,
        data: requestBody,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        print('---------response.data---------');
        print(response.data);
        return response.data;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(
        e,
        'Failed to upload agent file',
      );
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }
}
