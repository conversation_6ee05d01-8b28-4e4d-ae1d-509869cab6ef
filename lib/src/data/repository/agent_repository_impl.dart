import 'package:dio/dio.dart';
import '../../core/network/dio_client.dart';
import '../../core/services/api_error_handler.dart';
import '../../core/network/api_config.dart';
import '../../core/config/app_strings.dart';
import '../../core/services/exceptions.dart';
import '../../domain/models/agent_model.dart';
import '../../domain/repository/agent_repository.dart';

class AgentRepositoryImpl extends AgentRepository {
  AgentRepositoryImpl();

  static const String baseUrl = APIConfig.baseUrl;
  static const String agentsSearchUrl = APIConfig.agentsSearch;
  static const String agentsCreateUrl = APIConfig.agentCreate;

  @override
  Future<ApiAgentResponse> getAgents(Map<String, dynamic> requestBody) async {
    try {
      final dio = await DioClient.getDio();

      final response = await dio.post(agentsSearchUrl, data: requestBody);

      if (response.statusCode == 200) {
        return ApiAgentResponse.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, failedToFetchAgents);
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<AgentModel> getAgentById(String agentId) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get('$agentsSearchUrl/$agentId');

      if (response.statusCode == 200) {
        return AgentModel.fromJson(response.data['data'] ?? response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, failedToFetchAgentDetails);
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<dynamic> createAgent(Map<String, dynamic> requestBody) async {
    try {
      // print('requestBody: $requestBody');
      // final dio = await DioClient.getDio();
      // print('Request Headers: ${dio}');

      // print('Request Headers: ${dio.options}');
      // print('Request Headers: ${dio.options.headers}');
      // print('Request Headers: ${dio.options.baseUrl}');
      // print('Request URL: ${agentsCreateUrl}');
      // final response = await dio.post(agentsCreateUrl, data: requestBody);
      final _dio = await DioClient.getDio();
      final response = await _dio.post(agentsCreateUrl, data: requestBody);
      print('---------response.data****---------');
      print(response);
      if (response.statusCode == 200 || response.statusCode == 201) {
        print('---------response.data---------');
        print(response.data);
        return true;
      } else {
        return false;
      }
    } on DioException catch (e) {
      print('error creating agent ****: ${e.toString()}');
      throw ApiErrorHandler.handleDioException(e, 'Failed to create agent');
    } catch (e) {
      print('error creating agent api exception: ${e.toString()}');

      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<bool> uploadAgentFile(Map<String, dynamic> requestBody) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.post(
        APIConfig.agentFileUpload,
        data: requestBody,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        print('---------response.data---------');
        print(response.data);
        return response.data;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(
        e,
        'Failed to upload agent file',
      );
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }
}
