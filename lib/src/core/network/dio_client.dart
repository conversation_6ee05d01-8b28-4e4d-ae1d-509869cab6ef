import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../../data/repository/get_access_token_repository_impl.dart';
import '../../domain/repository/get_access_token_repository.dart';
import '../services/flutter_secure_storage.dart';
import '/src/core/network/api_config.dart';
import 'dio_interceptor.dart';

class DioClient {
  DioClient._();

  static const String baseUrl = APIConfig.baseUrl;
  static final SessionManager _sessionManager = SessionManager();
  static final GetAccessTokenRepository _getAccessTokenRepository =
      GetAccessTokenRepositoryImpl();

  static Future<Dio> getDio() async {
    final token = await _sessionManager.getToken();
    print('token: $token');
    final dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
      ),
    );
    print('Dio initialized with base URL: $baseUrl');
    print('Dio L: $dio');

    // In your createAgent method, before the API call:
    print('Request Headers: ${dio.options.headers}');
    print('Request URL: ${APIConfig.agentCreate}');
    dio.interceptors.add(DioInterceptor(_getAccessTokenRepository));
    return dio;
  }
}
