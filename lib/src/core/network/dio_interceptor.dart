import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '/src/core/services/flutter_secure_storage.dart';
import '../../domain/repository/get_access_token_repository.dart';

class DioInterceptor extends Interceptor {
  final GetAccessTokenRepository _getAccessTokenRepository;
  final SessionManager _sessionManager = SessionManager();

  DioInterceptor(this._getAccessTokenRepository);

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      try {
        // final refreshToken = await _sessionManager.getRefreshToken();

        // if (refreshToken != null) {
        //   final newToken = await _getAccessTokenRepository.getAccessToken(
        //     refreshToken,
        //   );

        //   if (newToken != null) {
        //     await _sessionManager.saveSession(newToken);

        //     final RequestOptions options = err.requestOptions;
        //     options.headers['Authorization'] = 'Bearer ${newToken.jwt}';

        //     final Dio dio = Dio();
        //     final response = await dio.fetch(options);

        //     return handler.resolve(response);
        //   }
        // }
      } catch (e) {
        debugPrint('Token refresh failed: $e');
      }
    }

    return handler.next(err);
  }
}
