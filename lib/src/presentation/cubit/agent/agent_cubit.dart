import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import '../../../core/services/exceptions.dart';
import '../../../domain/models/agent_model.dart';
import '../../../domain/repository/agent_repository.dart';

part 'agent_state.dart';

class AgentCubit extends Cubit<AgentState> {
  final AgentRepository _agentRepository;

  AgentCubit(this._agentRepository) : super(AgentInitial());

  /// Get list of agents with POST request
  Future<void> getAgents({
    int page = 0,
    int size = 20,
    String sortBy = "id",
    String sortDirection = "ASC",
    String searchString = "",
    DateTime? joiningDate,
    required String userId,
    bool loadMore = false,
  }) async {
    if (!loadMore) {
      emit(AgentLoading());
    }

    try {
      // Build request body in cubit
      final Map<String, dynamic> requestBody = {
        'page': page,
        'size': size,
        'sortBy': sortBy,
        'sortDirection': sortDirection,
        'searchString': searchString,
        'joiningDate': joiningDate?.toIso8601String(),
        'userId': userId,
      };

      final response = await _agentRepository.getAgents(requestBody);

      emit(
        AgentLoaded(
          agents: response.content,
          totalCount: response.totalElements,
          currentPage: response.number,
          hasMore: !response.last,
        ),
      );
    } on ApiException catch (e) {
      emit(AgentError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        AgentError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  /// Get agent details by ID
  Future<void> getAgentById(String agentId) async {
    emit(AgentLoading());

    try {
      final agent = await _agentRepository.getAgentById(agentId);
      emit(
        AgentLoaded(
          agents: [agent],
          totalCount: 1,
          currentPage: 0,
          hasMore: false,
        ),
      );
    } on ApiException catch (e) {
      emit(AgentError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        AgentError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  /// Clear current state
  void clearAgents() {
    emit(AgentInitial());
  }

  /// Create a new agent
  Future<void> createAgent(Map<String, dynamic> requestBody) async {
    try {
      final agent = await _agentRepository.createAgent(requestBody);
      emit(
        AgentCreated(
          recruiterId: agent.id,
          firstName: agent.firstName,
          lastName: agent.lastName,
          phone: agent.phone,
          email: agent.email,
          agentLicenseId: agent.id,
          city: agent.city,
          state: agent.state,
          postalCode: agent.postal,
          country: agent.country,
          additionalInfo: agent.additionalInfo,
        ),
      );
    } on ApiException catch (e) {
      emit(AgentError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        AgentError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  /// Upload agent file
  Future<void> uploadAgentFile(Map<String, dynamic> requestBody) async {
    try {
      final response = await _agentRepository.uploadAgentFile(requestBody);
      emit(
        AgentFileUploaded(
          userId: response.userId,
          categoryType: response.categoryType,
          documentType: response.documentType,
          dofilecumentType: response.dofilecumentType,
        ),
      );
    } on ApiException catch (e) {
      emit(AgentError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        AgentError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }
}
