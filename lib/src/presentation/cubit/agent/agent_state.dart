part of 'agent_cubit.dart';

@immutable
sealed class AgentState {}

final class AgentInitial extends AgentState {}

final class AgentLoading extends AgentState {}

final class AgentLoaded extends AgentState {
  final List<AgentModel> agents;
  final int totalCount;
  final int currentPage;
  final bool hasMore;

  AgentLoaded({
    required this.agents,
    required this.totalCount,
    required this.currentPage,
    this.hasMore = false,
  });
}

final class AgentError extends AgentState {
  final String message;
  final int? statusCode;

  AgentError({required this.message, this.statusCode});
}

final class AgentCreated extends AgentState {
  final String recruiterId;
  final String firstName;
  final String lastName;
  final String phone;
  final String email;
  final String agentLicenseId;
  final String city;
  final String state;
  final String postalCode;
  final String country;
  final String additionalInfo;

  AgentCreated({
    required this.recruiterId,
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.email,
    required this.agentLicenseId,
    required this.city,
    required this.state,
    required this.postalCode,
    required this.country,
    required this.additionalInfo,
  });
}

final class AgentFileUploaded extends AgentState {
  final String userId;
  final String categoryType;
  final String documentType;
  final String dofilecumentType;

  AgentFileUploaded({
    required this.userId,
    required this.categoryType,
    required this.documentType,
    required this.dofilecumentType,
  });
}
